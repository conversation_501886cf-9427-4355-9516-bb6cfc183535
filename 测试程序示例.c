/*
 * 测试程序示例 - 用于测试HMI与AD9959控制功能
 * 
 * 此文件展示了如何使用移植后的代码进行测试
 * 注意：这不是实际的工程文件，仅用于演示
 */

#include <stdio.h>
#include <stdint.h>

// 模拟发送测试命令的函数
void send_test_command(uint8_t* data, int len)
{
    printf("发送命令: ");
    for(int i = 0; i < len; i++)
    {
        if(data[i] >= 32 && data[i] <= 126) // 可打印字符
            printf("'%c' ", data[i]);
        else
            printf("0x%02X ", data[i]);
    }
    printf("\n");
}

int main()
{
    printf("=== HMI与AD9959控制功能测试 ===\n\n");
    
    // 测试1: 握手信号
    printf("测试1: 发送握手信号\n");
    uint8_t handshake[] = {0xfd, 0xff, 0xff, 0xff};
    send_test_command(handshake, 4);
    printf("期望回复: OKK\n\n");
    
    // 测试2: 频率增加
    printf("测试2: 频率增加100Hz\n");
    uint8_t freq_inc[] = {'1'};
    send_test_command(freq_inc, 1);
    printf("期望结果: combined += 100, AD9959频率更新, HMI显示更新\n\n");
    
    // 测试3: 频率减少
    printf("测试3: 频率减少100Hz\n");
    uint8_t freq_dec[] = {'2'};
    send_test_command(freq_dec, 1);
    printf("期望结果: combined -= 100, AD9959频率更新, HMI显示更新\n\n");
    
    // 测试4: 设置具体频率 (例如: 1000000Hz = 1MHz)
    printf("测试4: 设置频率为1MHz (1000000Hz)\n");
    uint32_t target_freq = 1000000;
    uint8_t freq_set[] = {
        '3',
        (uint8_t)(target_freq & 0xFF),        // 低字节
        (uint8_t)((target_freq >> 8) & 0xFF),
        (uint8_t)((target_freq >> 16) & 0xFF),
        (uint8_t)((target_freq >> 24) & 0xFF) // 高字节
    };
    send_test_command(freq_set, 5);
    printf("期望结果: combined = %u, AD9959频率设置为%uHz, HMI显示更新\n\n", 
           target_freq, target_freq);
    
    // 测试5: 浮点数增加
    printf("测试5: 浮点数增加0.1\n");
    uint8_t float_inc[] = {'4'};
    send_test_command(float_inc, 1);
    printf("期望结果: test_float += 0.1, HMI显示更新\n\n");
    
    // 测试6: 浮点数减少
    printf("测试6: 浮点数减少0.1\n");
    uint8_t float_dec[] = {'5'};
    send_test_command(float_dec, 1);
    printf("期望结果: test_float -= 0.1, HMI显示更新\n\n");
    
    // 测试7: 清除波形
    printf("测试7: 清除波形显示\n");
    uint8_t wave_clear[] = {'6'};
    send_test_command(wave_clear, 1);
    printf("期望结果: HMI波形显示清除\n\n");
    
    printf("=== 测试完成 ===\n");
    printf("注意事项:\n");
    printf("1. 确保串口2连接正确，波特率115200\n");
    printf("2. 确保AD9959硬件连接正确\n");
    printf("3. 确保HMI设备连接到串口2\n");
    printf("4. 观察HMI显示和AD9959输出频率变化\n");
    
    return 0;
}

/*
 * 频率计算示例:
 * 
 * 要设置10MHz (10000000Hz):
 * uint32_t freq = 10000000;
 * 命令: '3' + (freq & 0xFF) + ((freq>>8) & 0xFF) + ((freq>>16) & 0xFF) + ((freq>>24) & 0xFF)
 * 
 * 要设置100kHz (100000Hz):
 * uint32_t freq = 100000;
 * 命令: '3' + (freq & 0xFF) + ((freq>>8) & 0xFF) + ((freq>>16) & 0xFF) + ((freq>>24) & 0xFF)
 */
