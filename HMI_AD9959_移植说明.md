# HMI与AD9959控制代码移植说明

## 概述
本文档说明了如何将标准库的HMI通信和AD9959频率控制代码移植到HAL库工程中。

## 移植内容

### 1. 添加的变量
在 `main.c` 中添加了以下全局变量：
```c
uint8_t USART_RX_BUF[200]; // 串口接收缓冲区
uint16_t USART_RX_STA = 0;  // 接收状态标记
uint32_t combined = 0;      // 频率值
float test_float = 0.0f;    // 浮点测试值
```

### 2. HMI通信函数
实现了以下HMI通信函数，适配HAL库：
- `HMI_send_string()` - 发送字符串到HMI
- `HMI_send_number()` - 发送数字到HMI
- `HMI_send_float()` - 发送浮点数到HMI
- `HMI_Wave()` - 波形数据发送
- `HMI_Wave_Fast()` - 快速波形数据发送
- `HMI_Wave_Clear()` - 清除波形

### 3. 串口重定向
实现了 `fputc()` 函数，将printf输出重定向到串口2。

### 4. 串口中断接收
- 在 `usart.c` 中添加了 `HAL_UART_RxCpltCallback()` 回调函数
- 在 `stm32f4xx_it.c` 中添加了 `USART2_IRQHandler()` 中断处理函数
- 启用了USART2的NVIC中断

## 命令协议

### 单字节命令
- `'1'` (0x31) - 频率增加100Hz，同时控制AD9959通道0
- `'2'` (0x32) - 频率减少100Hz，同时控制AD9959通道0
- `'4'` (0x34) - 浮点数增加0.1
- `'5'` (0x35) - 浮点数减少0.1
- `'6'` (0x36) - 清除波形显示

### 多字节命令
- `'3'` + 4字节数据 - 设置具体频率值
  - 数据格式：低字节在前，高字节在后
  - 例如：`'3' + num1 + num2 + num3 + num4`
  - 频率值 = (num4 << 24) | (num3 << 16) | (num2 << 8) | num1

### 握手信号
- `0xfd 0xff 0xff 0xff` - 握手信号，MCU回复"OKK"

## AD9959频率控制
命令 `'1'`、`'2'`、`'3'` 会同时：
1. 更新HMI显示的频率值
2. 调用 `ad9959_write_frequency(AD9959_CHANNEL_0, combined)` 控制AD9959的通道0频率

## 使用方法

### 初始化
代码会在 `main()` 函数中自动：
1. 初始化AD9959
2. 设置初始频率和幅度
3. 启动串口中断接收

### 发送命令
通过串口2发送上述命令即可控制AD9959频率和HMI显示。

## 注意事项

1. **串口配置**：使用串口2 (USART2)，波特率115200
2. **中断优先级**：USART2中断优先级设置为0
3. **缓冲区大小**：接收缓冲区为200字节
4. **频率范围**：AD9959频率范围为1Hz到200MHz
5. **错误处理**：如果接收数据超过缓冲区大小，会自动重新开始接收

## 测试建议

1. 发送握手信号测试通信：`0xfd 0xff 0xff 0xff`
2. 发送单字节命令测试频率控制：`'1'` 或 `'2'`
3. 发送多字节命令测试具体频率设置：`'3' + 频率数据`

## 编译注意
确保在工程中包含了：
- `stdio.h` - 用于printf重定向
- `string.h` - 用于memset函数
- 所有相关的HAL库文件
