--cpu=Cortex-M4.fp.sp
"ad9959\startup_stm32f407xx.o"
"ad9959\main.o"
"ad9959\gpio.o"
"ad9959\usart.o"
"ad9959\stm32f4xx_it.o"
"ad9959\stm32f4xx_hal_msp.o"
"ad9959\stm32f4xx_hal_tim.o"
"ad9959\stm32f4xx_hal_tim_ex.o"
"ad9959\stm32f4xx_hal_uart.o"
"ad9959\stm32f4xx_hal_rcc.o"
"ad9959\stm32f4xx_hal_rcc_ex.o"
"ad9959\stm32f4xx_hal_flash.o"
"ad9959\stm32f4xx_hal_flash_ex.o"
"ad9959\stm32f4xx_hal_flash_ramfunc.o"
"ad9959\stm32f4xx_hal_gpio.o"
"ad9959\stm32f4xx_hal_dma_ex.o"
"ad9959\stm32f4xx_hal_dma.o"
"ad9959\stm32f4xx_hal_pwr.o"
"ad9959\stm32f4xx_hal_pwr_ex.o"
"ad9959\stm32f4xx_hal_cortex.o"
"ad9959\stm32f4xx_hal.o"
"ad9959\stm32f4xx_hal_exti.o"
"ad9959\system_stm32f4xx.o"
"ad9959\ad9959.o"
--library_type=microlib --strict --scatter "AD9959\AD9959.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "AD9959.map" -o AD9959\AD9959.axf